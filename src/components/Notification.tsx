import React, { <PERSON>actN<PERSON>, MouseEvent, useEffect, useState } from 'react';
import ReactDOM from 'react-dom';
import { BiX } from 'react-icons/bi';
import { HiCheckCircle, HiXCircle, HiInformationCircle, HiExclamationTriangle } from 'react-icons/hi2';

type notifType = "success" | "failure" | "info" | "warn"

interface NotificationToastProps {
    isOpen: boolean;
    renderObj: {
        leftSection?: ReactNode;
        midSection: ReactNode;
        rightSection?: ReactNode;
    }
    type: notifType
    duration?: number;
}

const typeConfig: Record<notifType, {
    bgColor: string;
    borderColor: string;
    textColor: string;
    iconColor: string;
    icon: ReactNode;
    progressColor: string;
    shadowColor: string;
}> = {
    "success": {
        bgColor: "bg-white",
        borderColor: "border-green/20",
        textColor: "text-headertext",
        iconColor: "text-green",
        icon: <HiCheckCircle size={20} />,
        progressColor: "bg-green",
        shadowColor: "shadow-green/10"
    },
    "failure": {
        bgColor: "bg-white",
        borderColor: "border-danger/20",
        textColor: "text-headertext",
        iconColor: "text-danger",
        icon: <HiXCircle size={20} />,
        progressColor: "bg-danger",
        shadowColor: "shadow-danger/10"
    },
    "info": {
        bgColor: "bg-white",
        borderColor: "border-info/20",
        textColor: "text-headertext",
        iconColor: "text-info",
        icon: <HiInformationCircle size={20} />,
        progressColor: "bg-info",
        shadowColor: "shadow-info/10"
    },
    "warn": {
        bgColor: "bg-white",
        borderColor: "border-orange/20",
        textColor: "text-headertext",
        iconColor: "text-orange",
        icon: <HiExclamationTriangle size={20} />,
        progressColor: "bg-orange",
        shadowColor: "shadow-orange/10"
    },
}

const NotificationToast: React.FC<NotificationToastProps> = ({ isOpen, renderObj, type, duration = 4000 }) => {
    const [isToastOpen, setIsToastOpen] = useState(isOpen);
    const [progress, setProgress] = useState(100);
    const [isPaused, setIsPaused] = useState(false);

    const config = typeConfig[type];

    useEffect(() => {
        setIsToastOpen(isOpen);
        if (isOpen) {
            setProgress(100);
        }
    }, [isOpen]);

    // Progress bar animation
    useEffect(() => {
        if (!isToastOpen || isPaused) return;

        const interval = setInterval(() => {
            setProgress((prev) => {
                const newProgress = prev - (100 / (duration / 50));
                if (newProgress <= 0) {
                    setIsToastOpen(false);
                    return 0;
                }
                return newProgress;
            });
        }, 50);

        return () => clearInterval(interval);
    }, [isToastOpen, isPaused, duration]);

    const handleClose = () => {
        setIsToastOpen(false);
    };

    const handleMouseEnter = () => {
        setIsPaused(true);
    };

    const handleMouseLeave = () => {
        setIsPaused(false);
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
        if (event.key === 'Escape') {
            handleClose();
        }
    };

    const notifRoot = typeof document !== 'undefined' ? document.getElementById('notification-root') : null;
    if (!notifRoot) return null;

    return ReactDOM.createPortal(
        <div
            className={`fixed z-[15] top-4 left-1/2 -translate-x-1/2 transition-all duration-700 ease-out transform ${
                isToastOpen
                    ? 'translate-y-0 opacity-100 scale-100 rotate-0'
                    : '-translate-y-8 opacity-0 scale-95 -rotate-1'
            }`}
            style={{
                filter: isToastOpen ? 'blur(0px)' : 'blur(4px)',
                transition: 'all 0.7s cubic-bezier(0.16, 1, 0.3, 1), filter 0.3s ease-out'
            }}
            role="alert"
            aria-live="polite"
            aria-atomic="true"
            tabIndex={-1}
        >
            <div
                className={`
                    ${config.bgColor} ${config.borderColor} ${config.textColor} ${config.shadowColor}
                    w-[calc(100vw-2rem)] max-w-[28rem] min-w-[20rem] mx-4 sm:mx-0 sm:w-auto
                    border-2 rounded-2xl shadow-xl backdrop-blur-sm
                    overflow-hidden relative
                    hover:shadow-2xl hover:scale-[1.02] hover:-translate-y-1
                    transition-all duration-300 ease-out
                    before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/5 before:to-transparent
                    before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700
                    focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500
                `}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
                onKeyDown={handleKeyDown}
            >
                {/* Progress bar */}
                <div className="absolute top-0 left-0 h-1 bg-outline/30 w-full rounded-t-2xl overflow-hidden">
                    <div
                        className={`h-full ${config.progressColor} transition-all duration-75 ease-linear rounded-t-2xl relative`}
                        style={{ width: `${progress}%` }}
                    >
                        {/* Shimmer effect on progress bar */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer" />
                    </div>
                </div>

                {/* Content */}
                <div className="flex items-start gap-3 p-4 pt-5 sm:gap-4 sm:p-5 sm:pt-6">
                    {/* Icon */}
                    <div className={`${config.iconColor} flex-shrink-0 mt-0.5`} aria-hidden="true">
                        {renderObj?.leftSection || config.icon}
                    </div>

                    {/* Message */}
                    <div className="flex-1 min-w-0">
                        <div className="text-[0.875rem] sm:text-[0.9375rem] font-medium leading-5 sm:leading-6 break-words hyphens-auto">
                            {renderObj?.midSection}
                        </div>
                        {renderObj?.rightSection && (
                            <div className="mt-1.5 sm:mt-2 text-[0.75rem] sm:text-[0.8125rem] text-subtext leading-4 sm:leading-5">
                                {renderObj.rightSection}
                            </div>
                        )}
                    </div>

                    {/* Close button */}
                    <button
                        onClick={handleClose}
                        className={`text-subtext hover:text-headertext hover:bg-grey rounded-lg p-1.5 sm:p-2 transition-all duration-200 flex-shrink-0 -mt-1 -mr-1 hover:scale-110 active:scale-95 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
                        aria-label={`Close ${type} notification`}
                        title="Press Escape or click to close"
                    >
                        <BiX size={16} className="sm:w-[18px] sm:h-[18px] group-hover:rotate-90 transition-transform duration-200" />
                    </button>
                </div>
            </div>
        </div>,
        notifRoot
    );
};

export default NotificationToast;
