"use client";

import { useNotification } from "@/contexts/NotificationProvider";
import AppButton from "@/components/AppButton";

export default function TestNotifications() {
    const { showNotification } = useNotification();

    const testNotifications = [
        {
            type: "success" as const,
            title: "Success Notification",
            message: "Your action was completed successfully!",
            longMessage: "Your profile has been updated successfully. All changes have been saved and will be reflected across the platform immediately.",
        },
        {
            type: "failure" as const,
            title: "Error Notification", 
            message: "Something went wrong. Please try again.",
            longMessage: "We encountered an unexpected error while processing your request. This could be due to a temporary server issue or network connectivity problems. Please check your internet connection and try again in a few moments.",
        },
        {
            type: "info" as const,
            title: "Info Notification",
            message: "Here's some important information for you.",
            longMessage: "We've updated our privacy policy to better protect your data and provide more transparency about how we collect, use, and share your information. Please review the changes at your convenience.",
        },
        {
            type: "warn" as const,
            title: "Warning Notification",
            message: "Please review this important warning.",
            longMessage: "Your account will expire in 7 days. To continue using our services without interruption, please renew your subscription or contact our support team for assistance.",
        },
    ];

    const showBasicNotification = (type: "success" | "failure" | "info" | "warn", message: string) => {
        showNotification({
            renderObj: { midSection: message },
            type,
            duration: 4000,
        });
    };

    const showLongNotification = (type: "success" | "failure" | "info" | "warn", message: string) => {
        showNotification({
            renderObj: { midSection: message },
            type,
            duration: 6000,
        });
    };

    const showNotificationWithDetails = (type: "success" | "failure" | "info" | "warn", message: string, details: string) => {
        showNotification({
            renderObj: { 
                midSection: message,
                rightSection: details
            },
            type,
            duration: 5000,
        });
    };

    const showCustomDurationNotification = (type: "success" | "failure" | "info" | "warn", message: string, duration: number) => {
        showNotification({
            renderObj: { midSection: `${message} (${duration/1000}s duration)` },
            type,
            duration,
        });
    };

    return (
        <div className="p-8 max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold text-headertext mb-8">Enhanced Notification System Test</h1>
            
            <div className="space-y-8">
                {/* Basic Notifications */}
                <section>
                    <h2 className="text-xl font-semibold text-headertext mb-4">Basic Notifications</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        {testNotifications.map((notif) => (
                            <AppButton
                                key={notif.type}
                                widthClass="w-full"
                                text={notif.title}
                                onClick={() => showBasicNotification(notif.type, notif.message)}
                            />
                        ))}
                    </div>
                </section>

                {/* Long Message Notifications */}
                <section>
                    <h2 className="text-xl font-semibold text-headertext mb-4">Long Message Notifications</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        {testNotifications.map((notif) => (
                            <AppButton
                                key={`long-${notif.type}`}
                                widthClass="w-full"
                                text={`Long ${notif.title}`}
                                onClick={() => showLongNotification(notif.type, notif.longMessage)}
                            />
                        ))}
                    </div>
                </section>

                {/* Notifications with Details */}
                <section>
                    <h2 className="text-xl font-semibold text-headertext mb-4">Notifications with Additional Details</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        {testNotifications.map((notif) => (
                            <AppButton
                                key={`details-${notif.type}`}
                                widthClass="w-full"
                                text={`${notif.title} + Details`}
                                onClick={() => showNotificationWithDetails(
                                    notif.type, 
                                    notif.message,
                                    "Additional context or action details here"
                                )}
                            />
                        ))}
                    </div>
                </section>

                {/* Custom Duration Notifications */}
                <section>
                    <h2 className="text-xl font-semibold text-headertext mb-4">Custom Duration Notifications</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <AppButton
                            widthClass="w-full"
                            text="Quick (2s)"
                            onClick={() => showCustomDurationNotification("info", "Quick notification", 2000)}
                        />
                        <AppButton
                            widthClass="w-full"
                            text="Normal (4s)"
                            onClick={() => showCustomDurationNotification("success", "Normal notification", 4000)}
                        />
                        <AppButton
                            widthClass="w-full"
                            text="Long (8s)"
                            onClick={() => showCustomDurationNotification("warn", "Long notification", 8000)}
                        />
                    </div>
                </section>

                {/* Instructions */}
                <section className="bg-grey p-6 rounded-xl">
                    <h2 className="text-xl font-semibold text-headertext mb-4">Testing Instructions</h2>
                    <ul className="space-y-2 text-subtext">
                        <li>• Click any button to trigger a notification</li>
                        <li>• Hover over notifications to pause the auto-dismiss timer</li>
                        <li>• Press Escape key to close the active notification</li>
                        <li>• Click the X button to manually close notifications</li>
                        <li>• Test on different screen sizes to verify responsiveness</li>
                        <li>• Try triggering multiple notifications quickly to test stacking</li>
                        <li>• Check accessibility with screen readers and keyboard navigation</li>
                    </ul>
                </section>
            </div>
        </div>
    );
}
